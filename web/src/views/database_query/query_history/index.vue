<template>
  <div class="main-container">
    <TableBody>
      <template #header>
        <TableHeader :show-filter="true">
          <template #search-content>
            <n-form inline label-placement="left">
              <n-form-item label="数据库连接">
                <n-select
                  v-model:value="searchParams.connection_id"
                  placeholder="选择数据库连接"
                  :options="connectionOptions"
                  clearable
                  style="width: 200px"
                />
              </n-form-item>
              <n-form-item label="执行状态">
                <n-select
                  v-model:value="searchParams.status"
                  placeholder="选择状态"
                  :options="statusOptions"
                  clearable
                  style="width: 120px"
                />
              </n-form-item>
              <n-form-item label="收藏状态">
                <n-select
                  v-model:value="searchParams.is_favorite"
                  placeholder="选择收藏状态"
                  :options="favoriteOptions"
                  clearable
                  style="width: 120px"
                />
              </n-form-item>
            </n-form>
          </template>
          <template #search-action>
            <n-button type="primary" @click="handleSearch">
              <template #icon>
                <n-icon :component="SearchOutline" />
              </template>
              搜索
            </n-button>
            <n-button @click="handleReset">
              <template #icon>
                <n-icon :component="RefreshOutline" />
              </template>
              重置
            </n-button>
          </template>
        </TableHeader>
      </template>
      <template #default>
        <n-data-table
          :loading="tableLoading"
          :columns="columns"
          :data="tableData"
          :row-key="rowKey"
          :style="{ minHeight: `${tableHeight}px` }"
          :flex-height="true"
        />
      </template>
      <template #footer>
        <TableFooter
          :pagination="pagination"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </template>
    </TableBody>

    <!-- SQL详情弹窗 -->
    <n-modal
      v-model:show="sqlModalVisible"
      preset="dialog"
      title="SQL详情"
      class="w-800px"
    >
      <n-tabs type="line" animated>
        <n-tab-pane name="sql" tab="SQL语句">
          <n-code
            :code="selectedSql"
            language="sql"
            :hljs="hljs"
            style="max-height: 400px; overflow: auto;"
          />
        </n-tab-pane>
        <n-tab-pane name="error" tab="错误信息" v-if="selectedError">
          <n-alert type="error" :title="selectedError" />
        </n-tab-pane>
      </n-tabs>
      <template #action>
        <n-space>
          <n-button @click="sqlModalVisible = false">关闭</n-button>
          <n-button type="primary" @click="handleCopyToExecutor">复制到执行器</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, h } from 'vue'
import { NButton, NTag, NPopconfirm, NIcon, useMessage } from 'naive-ui'
import { SearchOutline, RefreshOutline, StarOutline, Star, CopyOutline, TrashOutline } from '@vicons/ionicons5'
import { TableBody, TableHeader, TableFooter } from '@/components'
import { useCRUD } from '@/composables'
import api from '@/api/database_query'
import { useRouter } from 'vue-router'
import hljs from 'highlight.js/lib/core'
import sql from 'highlight.js/lib/languages/sql'

hljs.registerLanguage('sql', sql)

defineOptions({ name: 'QueryHistory' })

const $message = useMessage()
const router = useRouter()

const {
  tableData,
  tableLoading,
  tableHeight,
  pagination,
  searchParams,
  handlePageChange,
  handlePageSizeChange,
  handleSearch,
  handleReset
} = useCRUD({
  name: '查询历史',
  initForm: { connection_id: null, status: null, is_favorite: null },
  refresh: () => getTableData()
})

// 数据库连接选项
const connections = ref([])
const connectionOptions = computed(() => 
  connections.value.map(conn => ({
    label: `${conn.name} (${conn.db_type})`,
    value: conn.id
  }))
)

// 状态选项
const statusOptions = [
  { label: '成功', value: 'success' },
  { label: '失败', value: 'failed' }
]

// 收藏状态选项
const favoriteOptions = [
  { label: '已收藏', value: true },
  { label: '未收藏', value: false }
]

// SQL详情弹窗
const sqlModalVisible = ref(false)
const selectedSql = ref('')
const selectedError = ref('')

// 表格配置
const columns = [
  { 
    title: '查询名称', 
    key: 'query_name', 
    width: 150, 
    ellipsis: { tooltip: true },
    render: (row) => row.query_name || '未命名查询'
  },
  { 
    title: '数据库连接', 
    key: 'database_connection_name', 
    width: 150, 
    ellipsis: { tooltip: true } 
  },
  {
    title: 'SQL语句',
    key: 'sql_content',
    width: 300,
    ellipsis: { tooltip: true },
    render: (row) => {
      const preview = row.sql_content.length > 50 
        ? row.sql_content.substring(0, 50) + '...' 
        : row.sql_content
      return h('span', { 
        style: 'cursor: pointer; color: #18a058;',
        onClick: () => handleViewSql(row)
      }, preview)
    }
  },
  {
    title: '执行状态',
    key: 'status',
    width: 100,
    render: (row) => {
      return h(NTag, { 
        type: row.status === 'success' ? 'success' : 'error' 
      }, {
        default: () => row.status === 'success' ? '成功' : '失败'
      })
    }
  },
  { 
    title: '执行时间', 
    key: 'execution_time', 
    width: 100,
    render: (row) => row.execution_time ? `${row.execution_time}ms` : '-'
  },
  { 
    title: '结果行数', 
    key: 'result_count', 
    width: 100,
    render: (row) => row.result_count || '-'
  },
  { 
    title: '影响行数', 
    key: 'affected_rows', 
    width: 100,
    render: (row) => row.affected_rows || '-'
  },
  { title: '创建时间', key: 'created_at', width: 180 },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'center',
    fixed: 'right',
    render: (row) => {
      return [
        h(NButton, {
          size: 'small',
          type: row.is_favorite ? 'warning' : 'default',
          secondary: true,
          onClick: () => handleToggleFavorite(row)
        }, { 
          default: () => '收藏',
          icon: () => h(NIcon, { component: row.is_favorite ? Star : StarOutline })
        }),
        h(NButton, {
          size: 'small',
          type: 'primary',
          style: 'margin-left: 8px;',
          onClick: () => handleCopyToExecutor(row)
        }, { 
          default: () => '复用',
          icon: () => h(NIcon, { component: CopyOutline })
        }),
        h(NPopconfirm, {
          onPositiveClick: () => handleDeleteHistory(row.id),
          style: 'margin-left: 8px;'
        }, {
          default: () => '确认删除吗？',
          trigger: () => h(NButton, { 
            size: 'small', 
            type: 'error' 
          }, { 
            default: () => '删除',
            icon: () => h(NIcon, { component: TrashOutline })
          })
        })
      ]
    }
  }
]

const rowKey = (row) => row.id

// 获取表格数据
async function getTableData() {
  try {
    tableLoading.value = true
    const { data } = await api.getQueryHistory({
      page: pagination.page,
      page_size: pagination.pageSize,
      ...searchParams
    })
    tableData.value = data.data
    pagination.total = data.total
  } catch (error) {
    console.error('获取数据失败:', error)
  } finally {
    tableLoading.value = false
  }
}

// 获取数据库连接列表
async function getConnections() {
  try {
    const { data } = await api.getConnections({ page_size: 100 })
    connections.value = data.data.filter(conn => conn.is_active)
  } catch (error) {
    console.error('获取连接列表失败:', error)
  }
}

// 查看SQL详情
function handleViewSql(row) {
  selectedSql.value = row.sql_content
  selectedError.value = row.error_message
  sqlModalVisible.value = true
}

// 切换收藏状态
async function handleToggleFavorite(row) {
  try {
    await api.toggleFavorite(row.id)
    $message.success(row.is_favorite ? '取消收藏成功' : '收藏成功')
    getTableData()
  } catch (error) {
    $message.error('操作失败')
  }
}

// 复制到执行器
function handleCopyToExecutor(row) {
  if (!row) {
    // 如果是从弹窗调用的，使用选中的SQL
    row = {
      sql_content: selectedSql.value,
      database_connection_id: null,
      query_name: null
    }
  }

  // 将SQL语句存储到sessionStorage，然后跳转到执行器页面
  sessionStorage.setItem('copiedSql', JSON.stringify({
    sql_content: row.sql_content,
    database_connection_id: row.database_connection_id,
    query_name: row.query_name
  }))
  router.push('/database_query/sql_executor')
  $message.success('已复制到执行器')
}

// 删除查询历史
async function handleDeleteHistory(id) {
  try {
    await api.deleteQueryHistory(id)
    $message.success('删除成功')
    getTableData()
  } catch (error) {
    $message.error('删除失败')
  }
}

onMounted(() => {
  getConnections()
  getTableData()
})
</script>

<style scoped>
.main-container {
  height: calc(100vh - 100px);
}
</style>
